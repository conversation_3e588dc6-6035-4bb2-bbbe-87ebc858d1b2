# 🏢 Mulbin - Documentación de Desarrollo

> Red social para profesionales inmobiliarios - Backend Meteor 3 con APIs REST

## 📋 Tabla de Contenidos

- [🚀 Inicio Rápido](#-inicio-rápido)
- [🔧 Configuración del Entorno](#-configuración-del-entorno)
- [📂 Estructura del Proyecto](#-estructura-del-proyecto)
- [🌐 APIs Disponibles](#-apis-disponibles)
- [💾 Base de Datos](#-base-de-datos)
- [🔑 Autenticación](#-autenticación)
- [📝 Comandos Útiles](#-comandos-útiles)
- [🐛 Troubleshooting](#-troubleshooting)
- [📚 Referencias](#-referencias)

---

## 🚀 Inicio Rápido

### Prerequisitos

- Docker & Docker Compose
- Node.js 18+ (para desarrollo local opcional)

### Arrancar el proyecto

```bash
# Clonar y entrar al directorio
cd /ruta/al/proyecto/MulbinComponents

# Levantar servicios con Docker
docker compose up -d

# Ver logs en tiempo real
docker compose logs -f meteor-dev
```

**URLs disponibles:**

- 🌐 **Servidor principal:** http://localhost:3000
- 📡 **DDP (WebSocket):** ws://localhost:3000/websocket
- 🔌 **REST API:** http://localhost:3000/api/
- 💾 **MongoDB:** localhost:27017

---

## 🔧 Configuración del Entorno

### Variables de Entorno

Las variables se encuentran en `docker-compose.yml`:

```yaml
# Variables principales
MONGO_URL: mongodb://mongo:27017/mulbin_development
ROOT_URL: http://localhost:3000
PORT: 3000

# API Security
API_KEY: c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb

# Development
NODE_ENV: development
METEOR_ALLOW_SUPERUSER: true
```

### Modificar variables

```bash
# Editar docker-compose.yml
nano docker-compose.yml

# Reiniciar servicios
docker compose down
docker compose up -d
```

---

## 📂 Estructura del Proyecto

```
MulbinComponents/
├── app/
│   ├── imports/
│   │   ├── api/                    # APIs del backend
│   │   │   ├── rest/              # REST APIs para externos
│   │   │   │   ├── middleware/    # Autenticación & helpers
│   │   │   │   └── routes/        # Rutas REST
│   │   │   ├── users/             # Sistema de usuarios
│   │   │   ├── posts-inmobiliarios/ # Posts inmobiliarios
│   │   │   ├── comentarios-post/  # Sistema de comentarios
│   │   │   └── notifications/     # Notificaciones
│   │   ├── startup/
│   │   │   └── server/            # Configuración del servidor
│   │   └── utils/                 # Utilidades generales
│   ├── server/                    # Punto de entrada del servidor
│   ├── client/                    # Cliente (mínimo para Meteor)
│   └── package.json
├── docker-compose.yml             # Configuración Docker
└── README_DESARROLLO.md          # Esta documentación
```

---

## 🌐 APIs Disponibles

### 1. **REST API** (Para PHP y sistemas externos)

#### Autenticación

Todas las rutas protegidas requieren:

```http
Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb
```

#### Usuarios

```http
# Listar usuarios
GET /api/users?page=1&limit=20&search=juan

# Crear usuario
POST /api/users
Content-Type: application/json
{
  "email": "<EMAIL>",
  "password": "password123",
  "username": "usuario_ejemplo",
  "profile": {
    "firstName": "Juan",
    "lastName": "Pérez",
    "phone": "+523312345678",
    "company": "Inmobiliaria ABC",
    "license": "LIC-2024-001",
    "location": "norte",
    "specialties": ["residential", "commercial"],
    "bio": "Agente inmobiliario especializado...",
    "avatar": "https://ejemplo.com/avatar.jpg"
  }
}

# Actualizar usuario (FUNCIONAL)
PUT /api/update-user/{userId}
{
  "profile": {
    "bio": "Nueva biografía",
    "avatar": "https://nueva-imagen.jpg"
  }
}

# Verificar disponibilidad de username (NUEVO)
GET /api/users/check-username/{username}
# Respuesta: {"exists": true/false, "message": "...", "user": {...}}

# Búsqueda avanzada
POST /api/users/search
{
  "query": "juan",
  "filters": {
    "location": "norte",
    "verified": true,
    "specialties": ["luxury"]
  },
  "page": 1,
  "limit": 10
}
```

#### Posts Inmobiliarios

```http
# Listar posts
GET /api/posts?type=venta&location=norte&page=1

# Crear post
POST /api/posts
{
  "type": "venta",
  "title": "Casa en venta",
  "description": "Hermosa casa...",
  "price": 2500000,
  "location": "norte",
  "bedrooms": 3,
  "bathrooms": 2,
  "area": 150,
  "propertyType": "house",
  "images": ["url1.jpg", "url2.jpg"],
  "features": ["pool", "garage", "garden"]
}

# Actualizar post
PUT /api/posts/{postId}

# Estadísticas de posts
GET /api/posts/stats?authorId={userId}
```

#### Comentarios

```http
# Listar comentarios de un post
GET /api/comments?postId={postId}&page=1

# Crear comentario
POST /api/comments
{
  "postId": "post123",
  "content": "Excelente propiedad",
  "parentId": null
}

# Dar like a comentario
POST /api/comments/{commentId}/like
```

#### Notificaciones

```http
# Listar notificaciones del usuario
GET /api/notifications?userId={userId}&page=1

# Crear notificación
POST /api/notifications
{
  "userId": "user123",
  "type": "like",
  "title": "Nuevo like",
  "message": "Juan le dio like a tu post",
  "data": {"postId": "post123"}
}

# Marcar como leída
PUT /api/notifications/{notificationId}/read
```

### 2. **DDP API** (Para frontends Vue.js)

```javascript
// Suscripciones
Meteor.subscribe("postsInmobiliarios", filtros);
Meteor.subscribe("comentariosPost", postId);
Meteor.subscribe("userNotifications");

// Métodos
Meteor.call("postsInmobiliarios.create", postData);
Meteor.call("users.updateProfile", profileData);
Meteor.call("comentarios.create", commentData);
```

---

## 💾 Base de Datos

### Colecciones Principales

```javascript
// Usuarios (Meteor.users)
{
  _id: "userId",
  emails: [{address: "<EMAIL>"}],
  username: "username",
  profile: {
    firstName: "Juan",
    lastName: "Pérez",
    phone: "+523312345678",
    company: "Inmobiliaria ABC",
    license: "LIC-2024-001",
    location: "norte", // norte, sur, este, oeste, centro
    specialties: ["residential", "commercial", "luxury"],
    verified: false,
    rating: 4.5,
    totalReviews: 10,
    avatar: "https://avatar.url",
    bio: "Descripción del agente...",
    joinDate: ISODate()
  },
  roles: ["user"],
  status: "active", // active, inactive
  createdAt: ISODate(),
  updatedAt: ISODate()
}

// Posts Inmobiliarios
{
  _id: "postId",
  type: "venta", // venta, renta, socio, intercambio
  title: "Casa en venta",
  description: "Descripción detallada...",
  price: 2500000,
  location: "norte", // norte, sur, este, oeste, centro

  // Estructura híbrida para performance
  authorId: "userId", // Fuente de verdad
  authorCache: { // Cache para evitar joins
    firstName: "Juan",
    lastName: "Pérez",
    avatar: "https://avatar.url",
    company: "Inmobiliaria ABC",
    verified: true,
    lastCacheUpdate: ISODate()
  },

  // Detalles de la propiedad
  bedrooms: 3,
  bathrooms: 2,
  area: 150,
  propertyType: "house", // house, apartment, office, land
  features: ["pool", "garage", "garden"],
  images: ["url1.jpg", "url2.jpg"],

  // Métricas
  views: 150,
  contacts: 12,
  favorites: 8,
  interestedCount: 5,
  commentsCount: 3,

  // Control
  active: true,
  createdAt: ISODate(),
  updatedAt: ISODate()
}

// Comentarios (estructura jerárquica)
{
  _id: "commentId",
  postId: "postId",
  content: "Excelente propiedad",

  // Estructura híbrida
  authorId: "userId",
  authorCache: {
    firstName: "Ana",
    lastName: "García",
    avatar: "https://avatar.url",
    verified: true,
    lastCacheUpdate: ISODate()
  },

  // Jerarquía
  parentId: null, // null para comentarios principales
  level: 0, // 0 = principal, 1+ = respuestas

  // Interacciones
  likes: 5,

  createdAt: ISODate(),
  updatedAt: ISODate()
}

// Notificaciones
{
  _id: "notificationId",
  userId: "destinatarioId",
  type: "like", // like, comment, post, follow
  title: "Nuevo like",
  message: "Juan le dio like a tu post",
  data: { // Datos adicionales específicos del tipo
    postId: "post123",
    authorId: "juan123"
  },
  read: false,
  createdAt: ISODate()
}
```

### Acceso a MongoDB

```bash
# Conectar a MongoDB
docker compose exec mongo mongosh mulbin_development

# Ver colecciones
show collections

# Consultar usuarios
db.users.find().pretty()

# Consultar posts
db.postsInmobiliarios.find({type: "venta"}).pretty()
```

---

## 🔑 Autenticación

### Para APIs REST

```http
Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb
```

### Para DDP (Vue.js)

```javascript
// Login
Meteor.loginWithPassword(email, password);

// Verificar autenticación
Meteor.userId(); // null si no está autenticado
```

### Permisos

- **read**: Leer datos
- **write**: Crear/actualizar datos
- **admin**: Operaciones administrativas

---

## 📝 Comandos Útiles

### Docker

```bash
# Levantar servicios
docker compose up -d

# Ver logs
docker compose logs -f meteor-dev

# Reiniciar solo Meteor
docker compose restart meteor-dev

# Entrar al contenedor
docker compose exec meteor-dev sh

# Parar todo
docker compose down

# Rebuild completo
docker compose down
docker compose build --no-cache
docker compose up -d
```

### Meteor (dentro del contenedor)

```bash
# Entrar al contenedor
docker compose exec meteor-dev sh

# Instalar paquetes
meteor add aldeed:collection2
meteor add matb33:collection-hooks

# Ver paquetes instalados
meteor list

# Reset de la base de datos
meteor reset

# Consola de Meteor
meteor shell
```

### Desarrollo

```bash
# Ver usuarios en tiempo real
docker compose logs -f meteor-dev | grep "👥"

# Monitorear requests REST
docker compose logs -f meteor-dev | grep "🔄"

# Ver errores
docker compose logs -f meteor-dev | grep "❌"

# Hacer backup de MongoDB
docker compose exec mongo mongodump --db mulbin_development --out /tmp/backup

# Restaurar backup
docker compose exec mongo mongorestore --db mulbin_development /tmp/backup/mulbin_development
```

### Testing de APIs

```bash
# Test rápido de usuarios
curl "http://localhost:3000/api/users" \
  -H "Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb"

# Test de creación de usuario
curl -X POST "http://localhost:3000/api/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb" \
  -d '{"email":"<EMAIL>","password":"password123","profile":{"firstName":"Test","lastName":"User"}}'

# Test de actualización (endpoint funcional)
curl -X PUT "http://localhost:3000/api/update-user/USER_ID" \
  -H "Content-Type: application/json" \
  -H "Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb" \
  -d '{"profile":{"bio":"Nueva bio"}}'
```

---

## 🐛 Troubleshooting

### Problemas Comunes

#### 1. **Error: "Cannot read properties of undefined"**

```bash
# Verificar que las dependencias estén instaladas
docker compose exec meteor-dev meteor list
```

#### 2. **Servidor no responde en localhost:3000**

```bash
# Verificar que los puertos no estén ocupados
lsof -i :3000
lsof -i :27017

# Verificar logs
docker compose logs meteor-dev
```

#### 3. **Error de autenticación REST**

- Verificar API Key en `docker-compose.yml`
- Verificar que el header `Authorization` esté correcto
- La API Key actual es: `c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb`

#### 4. **PUT requests se cuelgan**

- **Problema conocido**: Múltiples middlewares en `/api/users/` causan conflictos
- **Solución**: Usar `/api/update-user/{id}` que está probado y funcional

#### 5. **Peticiones REST sin autorización se cuelgan** ✅ SOLUCIONADO

- **Problema**: Peticiones sin header `Authorization` se colgaban en lugar de devolver error
- **Causa**: Faltaban `return` después de `res.end()` en middleware de autenticación
- **Síntoma**: Peticiones con clave incorrecta funcionaban, pero sin header se colgaban
- **Solución**: ✅ Agregados `return` faltantes y migrado a `findOneAsync()` para Meteor 3
- **Estado**: Funcionando correctamente desde Junio 2025

#### 6. **Error de Schema/Validation**

```bash
# Ver errores específicos en logs
docker compose logs meteor-dev | grep -i error

# Verificar estructura de datos
docker compose exec mongo mongosh mulbin_development
```

#### 7. **Base de datos no se conecta**

```bash
# Verificar que MongoDB esté corriendo
docker compose ps

# Reiniciar MongoDB
docker compose restart mongo

# Verificar conectividad
docker compose exec meteor-dev meteor shell
```

#### 8. **Creación de usuarios devuelve usuario incorrecto**

- **Problema**: `Accounts.createUser()` en Meteor 3 es asíncrono
- **Síntoma**: Devuelve usuario anterior en lugar del recién creado
- **Causa**: Falta `await` antes de `Accounts.createUser()`
- **Solución**: Usar `await Accounts.createUser({...})` en lugar de `Accounts.createUser({...})`

### Logs Importantes

```bash
# Error logs
docker compose logs meteor-dev | grep "❌\|ERROR\|Error"

# Success logs
docker compose logs meteor-dev | grep "✅\|SUCCESS"

# API requests
docker compose logs meteor-dev | grep "🔄\|📝\|💾"

# Startup logs
docker compose logs meteor-dev | grep "🚀\|🌐\|📡"
```

---

## 📚 Referencias

### Tecnologías Utilizadas

- **[Meteor 3](https://docs.meteor.com/)** - Framework full-stack
- **[MongoDB](https://docs.mongodb.com/)** - Base de datos NoSQL
- **[SimpleSchema](https://github.com/aldeed/simple-schema-js)** - Validación de esquemas
- **[Collection2](https://github.com/aldeed/meteor-collection2)** - Integración schema-collection
- **[Collection Hooks](https://github.com/Meteor-Community-Packages/meteor-collection-hooks)** - Hooks de base de datos

### Arquitectura del Sistema

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   Vue.js        │◄──►│   Meteor 3      │◄──►│   MongoDB       │
│   (DDP/WS)      │    │   (Node.js)     │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                               ▲
                               │ REST API
                               ▼
                       ┌─────────────────┐
                       │   PHP Backend   │
                       │   (External)    │
                       └─────────────────┘
```

### Integración con Sistema Padre

El sistema Meteor funciona como **backend especializado** dentro de un sistema tradicional más grande:

```
┌─────────────────────────────────────────┐
│           SISTEMA PADRE (PHP)           │
│  ┌─────────────────┐ ┌─────────────────┐│
│  │   Frontend      │ │   Backend       ││
│  │   (HTML/CSS/JS) │ │   (PHP)         ││
│  │                 │ │                 ││
│  │  ┌──────────────┤ │                 ││
│  │  │Vue Components│◄┼─┤ REST Client    ││
│  │  └──────────────┤ │ │               ││
│  └─────────────────┘ └─┼───────────────┘│
└─────────────────────────┼─────────────────┘
                          │ HTTP REST API
                          ▼
                ┌─────────────────┐
                │   METEOR API    │
                │   (Port 3000)   │
                └─────────────────┘
```

#### Flujo de Integración Típico

1. **Sistema Padre** renderiza página con formulario de registro
2. **Javascript del padre** hace request a Meteor para verificar username
3. Si disponible, **Sistema Padre** procede con creación vía REST
4. **Componentes Vue** se conectan vía DDP para funcionalidad avanzada

#### Ejemplo de Verificación Pre-Registro

```php
// En el sistema padre (PHP)
function validateUsernameBeforeRegistration($username) {
    $meteormAPI = new MulbinAPI();

    // Verificar en Meteor antes de continuar
    $check = $meteormAPI->checkUsername($username);

    if ($check['data']['exists']) {
        return [
            'valid' => false,
            'message' => 'Username no disponible',
            'suggestion' => $username . '_' . date('Y')
        ];
    }

    return ['valid' => true, 'message' => 'Username disponible'];
}
```

### Flujos de Datos

#### Sistema Híbrido Posts/Comentarios

1. **Creación**: Se guarda `authorId` + `authorCache` actual
2. **Lectura**: Se lee directamente el cache (rápido)
3. **Actualización de perfil**: Hook automático sincroniza cache en todos los posts/comentarios
4. **Beneficios**: Performance + Consistencia

#### Notificaciones

1. **Trigger**: Acción del usuario (like, comment, etc.)
2. **Creación**: Se inserta notificación para destinatario
3. **Delivery**: Frontend se suscribe y recibe en tiempo real
4. **Gestión**: Usuario puede marcar como leída, eliminar

### Mejores Prácticas

#### Para Desarrollo

1. **Siempre revisar logs** antes de hacer cambios
2. **Usar endpoints de prueba** antes de modificar existentes
3. **Verificar autenticación** en cada request REST
4. **Documentar cambios** en este archivo

#### Para APIs REST

1. **Usar API Key correcta** en headers
2. **Validar JSON** antes de enviar
3. **Manejar errores** apropiadamente
4. **Usar rutas específicas** cuando sea necesario (ej: `/api/update-user/`)

#### Para Schemas

1. **No usar propiedades inválidas** como `decimal`
2. **Validar tipos de datos** correctamente
3. **Usar hooks** para sincronización automática
4. **Manejar campos opcionales** apropiadamente

---

## 🔄 Changelog

### Versión Actual (Diciembre 2024)

- ✅ Meteor 3 + MongoDB funcionando
- ✅ Sistema de usuarios completo con perfiles inmobiliarios
- ✅ Posts inmobiliarios con estructura híbrida
- ✅ Comentarios jerárquicos con likes
- ✅ Sistema de notificaciones en tiempo real
- ✅ REST APIs para integración PHP
- ✅ Endpoint PUT funcional (`/api/update-user/`)
- ✅ Sincronización automática de cache
- ✅ Autenticación con API Keys
- ✅ Docker setup completo
- ✅ **NUEVO**: Fix problema peticiones REST sin autorización (Junio 2025)
- ✅ **NUEVO**: Endpoint verificación de username `/api/users/check-username/{username}` (Junio 2025)

### Próximas Mejoras

- [ ] Optimizar rutas REST conflictivas
- [ ] Implementar rate limiting
- [ ] Agregar tests automatizados
- [ ] Documentación de API con Swagger
- [ ] Sistema de archivos/imágenes
- [ ] Notificaciones push

---

**📞 Contacto:** Para dudas o mejoras, consultar este archivo primero, luego buscar en logs, y finalmente preguntar al equipo.

**🎯 Objetivo:** Desarrollo rápido y eficiente sin "cabecear" innecesariamente.
