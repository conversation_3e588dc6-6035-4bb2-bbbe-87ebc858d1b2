# Ejemplo: Creación de Usuario con Token de Autenticación Automático

## Descripción

Este ejemplo muestra cómo crear un usuario y usar inmediatamente el token de autenticación generado automáticamente para realizar operaciones posteriores sin necesidad de hacer login.

## Flujo Completo

### 1. <PERSON><PERSON><PERSON>

```bash
curl -X POST "http://localhost:3000/api/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "username": "corretor_juan",
    "profile": {
      "firstName": "Juan",
      "lastName": "Pérez",
      "phone": "+523312345678",
      "company": "Inmobiliaria ABC",
      "license": "LIC-2024-001",
      "location": "norte",
      "specialties": ["residential", "commercial"],
      "bio": "Especialista en propiedades residenciales y comerciales",
      "avatar": "https://cdn.example.com/avatars/juan-perez.jpg"
    }
  }'
```

### 2. Respuesta con Token

```json
{
  "message": "Usuario creado exitosamente",
  "userId": "abc123...",
  "user": {
    "_id": "abc123...",
    "username": "corretor_juan",
    "emails": [{"address": "<EMAIL>"}],
    "profile": {
      "firstName": "Juan",
      "lastName": "Pérez",
      "company": "Inmobiliaria ABC",
      "verified": true
    }
  },
  "authToken": "lRNslQZmswkG-8-nG6gh9Uyq7T86ltF6lhWvrvdk4-G",
  "tokenInfo": {
    "type": "Bearer",
    "usage": "Usar como 'Authorization: Bearer ...' en requests posteriores",
    "expiresAt": "2025-06-05T23:59:59.000Z"
  }
}
```

### 3. Usar Token Inmediatamente

```bash
# Obtener información del usuario
curl -X GET "http://localhost:3000/api/users/abc123..." \
  -H "Authorization: Bearer lRNslQZmswkG-8-nG6gh9Uyq7T86ltF6lhWvrvdk4-G"

# Actualizar perfil del usuario
curl -X PUT "http://localhost:3000/api/update-user/abc123..." \
  -H "Authorization: Bearer lRNslQZmswkG-8-nG6gh9Uyq7T86ltF6lhWvrvdk4-G" \
  -H "Content-Type: application/json" \
  -d '{"profile": {"bio": "Nueva biografía actualizada"}}'

# Crear un post inmobiliario
curl -X POST "http://localhost:3000/api/posts" \
  -H "Authorization: Bearer lRNslQZmswkG-8-nG6gh9Uyq7T86ltF6lhWvrvdk4-G" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "venta",
    "title": "Mi primera propiedad",
    "description": "Hermosa casa en venta",
    "price": 2500000,
    "location": "norte",
    "authorId": "abc123..."
  }'
```

## Ejemplo en PHP

```php
<?php
class MulbinUserManager {
    private $apiUrl = 'http://localhost:3000/api/';
    private $apiKey = 'c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb';
    
    public function createUserWithToken($userData) {
        // 1. Crear usuario
        $response = $this->makeRequest('users', 'POST', $userData, $this->apiKey);
        
        if ($response['status'] !== 201) {
            throw new Exception('Error creando usuario: ' . $response['data']['error']);
        }
        
        $result = $response['data'];
        $userToken = $result['authToken'];
        $userId = $result['userId'];
        
        echo "✅ Usuario creado: {$result['user']['username']}\n";
        echo "🔐 Token generado: " . substr($userToken, 0, 20) . "...\n";
        
        // 2. Usar token inmediatamente para crear un post
        $postData = [
            'type' => 'venta',
            'title' => 'Mi primera propiedad',
            'description' => 'Propiedad registrada automáticamente',
            'price' => 2000000,
            'location' => $result['user']['profile']['location'],
            'authorId' => $userId
        ];
        
        $postResponse = $this->makeRequest('posts', 'POST', $postData, $userToken, true);
        
        if ($postResponse['status'] === 201) {
            echo "✅ Post creado automáticamente\n";
        }
        
        return [
            'user' => $result['user'],
            'token' => $userToken,
            'userId' => $userId
        ];
    }
    
    private function makeRequest($endpoint, $method, $data, $token, $isUserToken = false) {
        $url = $this->apiUrl . $endpoint;
        
        $headers = [
            'Content-Type: application/json',
            'Authorization: ' . ($isUserToken ? 'Bearer ' : 'ApiKey ') . $token
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        
        if ($data && in_array($method, ['POST', 'PUT'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return [
            'status' => $httpCode,
            'data' => json_decode($response, true)
        ];
    }
}

// Uso
$manager = new MulbinUserManager();

$newUser = [
    'email' => '<EMAIL>',
    'password' => 'password123',
    'username' => 'nuevo_corretor',
    'profile' => [
        'firstName' => 'María',
        'lastName' => 'González',
        'company' => 'Bienes Raíces MG',
        'location' => 'sur'
    ]
];

try {
    $result = $manager->createUserWithToken($newUser);
    echo "🎉 Proceso completado exitosamente\n";
    echo "Usuario ID: {$result['userId']}\n";
    echo "Token para uso futuro: {$result['token']}\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
```

## Ventajas

- ✅ **Sin login adicional**: El usuario puede usar el sistema inmediatamente
- ✅ **Autenticación segura**: Usa el sistema nativo de tokens de Meteor
- ✅ **Compatible con resume**: El token funciona con DDP también
- ✅ **Persistente**: El token permanece válido hasta que expire
- ✅ **Escalable**: No requiere sesiones del lado del servidor

## Casos de Uso

1. **Registro e inmediata publicación**: Usuario se registra y publica su primera propiedad
2. **Integración con sistemas externos**: PHP puede crear usuarios y usar tokens
3. **Aplicaciones móviles**: Registro y autenticación en un solo paso
4. **Onboarding automatizado**: Crear perfil completo tras el registro
