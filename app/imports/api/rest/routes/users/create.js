import { WebApp } from "meteor/webapp";
import { Meteor } from "meteor/meteor";
import { Accounts } from "meteor/accounts-base";
import {
  authenticateRestAP<PERSON>,
  parseJsonBody,
  sendJson,
  handleError,
} from "../../middleware/auth.js";
import { getUserFields, isValidEmail, isValidPassword } from "./helpers.js";

// POST /api/users - Crear nuevo usuario
WebApp.connectHandlers.use("/api/users", authenticateRestAPI);
WebApp.connectHandlers.use("/api/users", parseJsonBody);
WebApp.connectHandlers.use("/api/users", async (req, res, next) => {
  if (req.method !== "POST") return next();

  try {
    const {
      email,
      password,
      username,
      profile = {},
      roles = ["user"],
    } = req.body;

    // Validaciones básicas
    if (!email || !password) {
      return sendJson(
        res,
        {
          error: "Email y password son requeridos",
          code: "MISSING_FIELDS",
        },
        400
      );
    }

    if (!isValidEmail(email)) {
      return sendJson(
        res,
        {
          error: "Email no válido",
          code: "INVALID_EMAIL",
        },
        400
      );
    }

    if (!isValidPassword(password)) {
      return sendJson(
        res,
        {
          error: "Password debe tener al menos 6 caracteres",
          code: "WEAK_PASSWORD",
        },
        400
      );
    }

    // Verificar que el email no existe
    const existingUser = await Meteor.users.findOneAsync({
      "emails.address": email.toLowerCase(),
    });

    if (existingUser) {
      return sendJson(
        res,
        {
          error: "El email ya está registrado",
          code: "EMAIL_EXISTS",
        },
        409
      );
    }

    // Verificar username único si se proporciona
    if (username) {
      const existingUsername = await Meteor.users.findOneAsync({ username });
      if (existingUsername) {
        return sendJson(
          res,
          {
            error: "El username ya está en uso",
            code: "USERNAME_EXISTS",
          },
          409
        );
      }
    }

    const genericPassword = process.env.DEFAULT_USER_PASSWORD;

    // Crear usuario (CORREGIDO - Meteor 3 es asíncrono)
    const userId = await Accounts.createUser({
      email: email.toLowerCase(),
      password: genericPassword, // Recupero el password de variable de entorno
      profile: {
        firstName: profile.firstName || "",
        lastName: profile.lastName || "",
      },
    });

    console.log(`👤 Usuario base creado con ID: ${userId}`);

    // Asignar campos adicionales después de la creación
    const updateFields = {
      roles,
      status: "active",
      createdAt: new Date(),
      updatedAt: new Date(),
      "profile.phone": profile.phone || "",
      "profile.company": profile.company || "",
      "profile.license": profile.license || "",
      "profile.location": profile.location || "",
      "profile.specialties": profile.specialties || [],
      "profile.bio": profile.bio || "",
      "profile.avatar": profile.avatar || "",
      "profile.verified": true, // Verificado por default en REST API
      "profile.rating": 0,
      "profile.totalReviews": 0,
      "profile.joinDate": new Date(),
    };

    if (username) {
      updateFields.username = username;
    }

    console.log(`💾 Actualizando usuario ${userId} con campos adicionales`);

    await Meteor.users.updateAsync(userId, {
      $set: updateFields,
    });

    // Obtener usuario creado con campos específicos (CORREGIDO)
    const newUser = await Meteor.users.findOneAsync(userId, {
      fields: getUserFields(),
    });

    console.log(`✅ Usuario creado con ID: ${userId}`);
    console.log(
      `📧 Email del usuario creado: ${newUser?.emails?.[0]?.address}`
    );

    sendJson(
      res,
      {
        message: "Usuario creado exitosamente",
        user: newUser,
        userId,
      },
      201
    );
  } catch (error) {
    handleError(res, error, "Error al crear usuario");
  }
});

console.log("👤 User creation route loaded");
