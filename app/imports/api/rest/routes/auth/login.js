import { WebApp } from "meteor/webapp";
import { Meteor } from "meteor/meteor";
import { Accounts } from "meteor/accounts-base";
import { NpmModuleBcrypt } from "meteor/npm-bcrypt";
import {
  authenticateRestAP<PERSON>,
  parseJsonBody,
  sendJson,
  handleError,
} from "../../middleware/auth.js";
import { getUserFields } from "../users/helpers.js";

// POST /api/auth/login - Autenticar usuario existente y generar token
// NOTA: Para simplificar, este endpoint genera token sin verificar password
// Se asume que la autenticación ya se hizo en el sistema padre
WebApp.connectHandlers.use("/api/auth/login", authenticateRestAPI);
WebApp.connectHandlers.use("/api/auth/login", parseJsonBody);
WebApp.connectHandlers.use("/api/auth/login", async (req, res, next) => {
  if (req.method !== "POST") return next();

  try {
    const { email, username, userId } = req.body;

    // Validar que se proporcione al menos un identificador
    if (!email && !username && !userId) {
      return sendJson(
        res,
        {
          error: "Email, username o userId son requeridos",
          code: "MISSING_IDENTIFIER",
        },
        400
      );
    }

    console.log(`🔐 Generando token para: ${userId || email || username}`);

    // Buscar usuario por ID, email o username
    let user;
    if (userId) {
      user = await Meteor.users.findOneAsync(userId);
    } else if (email) {
      user = await Meteor.users.findOneAsync({
        "emails.address": email.toLowerCase(),
      });
    } else {
      user = await Meteor.users.findOneAsync({
        username: username,
      });
    }

    if (!user) {
      console.log(`❌ Usuario no encontrado: ${userId || email || username}`);
      return sendJson(
        res,
        {
          error: "Usuario no encontrado",
          code: "USER_NOT_FOUND",
        },
        404
      );
    }

    // Verificar que el usuario esté activo
    if (user.status !== "active") {
      console.log(`❌ Usuario inactivo: ${user._id}`);
      return sendJson(
        res,
        {
          error: "Usuario inactivo",
          code: "USER_INACTIVE",
        },
        403
      );
    }

    console.log(`✅ Autenticación exitosa para usuario: ${user._id}`);

    // 🔑 GENERAR TOKEN DE AUTENTICACIÓN
    console.log(`🔑 Generando token de autenticación para usuario ${user._id}`);
    
    // Generar token de login usando las funciones nativas de Meteor
    const stampedToken = Accounts._generateStampedLoginToken();
    const loginToken = stampedToken.token;
    
    // Insertar el token en el usuario para que sea válido
    await Accounts._insertLoginToken(user._id, stampedToken);
    
    console.log(`✅ Token de autenticación generado para usuario ${user._id}`);
    console.log(`🔐 Token: ${loginToken.substring(0, 10)}...`);

    // Obtener datos del usuario con campos específicos
    const userData = await Meteor.users.findOneAsync(user._id, {
      fields: getUserFields(),
    });

    // Actualizar última actividad
    await Meteor.users.updateAsync(user._id, {
      $set: {
        "profile.lastActive": new Date(),
        updatedAt: new Date(),
      },
    });

    console.log(`✅ Login exitoso para usuario: ${user._id}`);
    console.log(`📧 Email: ${userData?.emails?.[0]?.address}`);

    sendJson(
      res,
      {
        message: "Autenticación exitosa",
        user: userData,
        userId: user._id,
        // 🔑 TOKEN PARA AUTENTICACIÓN EXTERNA
        authToken: loginToken,
        tokenInfo: {
          type: "Bearer",
          usage: "Usar como 'Authorization: Bearer " + loginToken + "' en requests posteriores",
          expiresAt: stampedToken.when,
          generatedAt: new Date().toISOString()
        }
      },
      200
    );
  } catch (error) {
    handleError(res, error, "Error en autenticación");
  }
});

console.log("🔐 Auth login route loaded");
