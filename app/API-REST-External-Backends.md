# API REST - Documentación para Backends Externos

## Descripción General

Esta API REST permite que backends externos (PHP, Python, Node.js, etc.) interactúen con el sistema Mulbin (MeteorJS). Funciona en paralelo con el sistema DDP existente, manteniendo toda la funcionalidad para frontends Vue.js.

## Autenticación

La API soporta dos métodos de autenticación:

### 1. API Key (Recomendado para backends)

```bash
# Header de autorización
Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb

# O también
Authorization: c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb
```

### 2. Bearer <PERSON>ken (Para usuarios autenticados)

```bash
Authorization: Bearer <user-login-token>
```

### Variables de Entorno

```bash
API_KEY=c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb
```

## Base URL

Development Environment:

```
http://localhost:3000/api/
```

Production Environment:

```
https://ws-si.mulb.in:3000/api/
```

---

## 👥 USUARIOS

### Crear Usuario

```bash
POST /api/users
Content-Type: application/json
Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb

{
  "email": "<EMAIL>",
  "password": "password123",
  "username": "corretor_juan",
  "profile": {
    "firstName": "Juan",
    "lastName": "Pérez",
    "phone": "+523312345678",
    "company": "Inmobiliaria ABC",
    "license": "LIC-2024-001",
    "specialties": ["residential", "commercial"],
    "location": "norte",
    "bio": "Corretor especializado en propiedades residenciales y comerciales",
    "avatar": "https://cdn.example.com/avatars/juan-perez.jpg"
  },
  "roles": ["user"]
}
```

**Respuesta:**

```json
{
  "message": "Usuario creado exitosamente",
  "userId": "abc123...",
  "user": {
    "_id": "abc123...",
    "username": "corretor_juan",
    "emails": [{ "address": "<EMAIL>", "verified": false }],
    "profile": {
      "firstName": "Juan",
      "lastName": "Pérez",
      "phone": "+523312345678",
      "company": "Inmobiliaria ABC",
      "license": "LIC-2024-001",
      "specialties": ["residential", "commercial"],
      "location": "norte",
      "bio": "Corretor especializado en propiedades residenciales y comerciales",
      "avatar": "https://cdn.example.com/avatars/juan-perez.jpg",
      "verified": false,
      "rating": 0,
      "totalReviews": 0,
      "joinDate": "2024-01-01T00:00:00.000Z"
    },
    "roles": ["user"],
    "status": "active",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### Listar Usuarios

```bash
GET /api/users?page=1&limit=20&search=juan&location=norte&verified=true&status=active
```

### Obtener Usuario Específico

```bash
GET /api/users/abc123...
```

### Verificar Username (Nuevo - Para Sistema Padre)

```bash
GET /api/users/check-username/mi_username_exacto
```

**Respuesta si existe:**

```json
{
  "exists": true,
  "message": "Username ya está en uso",
  "user": {
    "id": "abc123...",
    "username": "mi_username_exacto",
    "firstName": "Juan",
    "lastName": "Pérez",
    "company": "Inmobiliaria ABC",
    "avatar": "https://avatar.jpg",
    "status": "active",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

**Respuesta si NO existe:**

```json
{
  "exists": false,
  "message": "Username disponible",
  "username": "mi_username_exacto"
}
```

### Actualizar Usuario

```bash
PUT /api/update-user/abc123...
Content-Type: application/json
Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb

{
  "profile": {
    "firstName": "Juan Carlos",
    "phone": "+523312345679",
    "avatar": "https://cdn.example.com/avatars/nueva-imagen.jpg"
  }
}
```

**Respuesta:**

```json
{
  "message": "Usuario actualizado exitosamente",
  "user": {
    "_id": "abc123...",
    "username": "corretor_juan",
    "emails": [{ "address": "<EMAIL>" }],
    "profile": {
      "firstName": "Juan Carlos",
      "lastName": "Pérez",
      "phone": "+523312345679",
      "company": "Inmobiliaria ABC",
      "avatar": "https://cdn.example.com/avatars/nueva-imagen.jpg",
      "verified": true,
      "rating": 4.5,
      "totalReviews": 10
    },
    "updatedAt": "2024-01-01T10:30:00.000Z"
  }
}
```

**Campos actualizables:**

- `profile.firstName` - Nombre
- `profile.lastName` - Apellidos
- `profile.phone` - Teléfono
- `profile.company` - Empresa
- `profile.avatar` - URL del avatar
- `profile.bio` - Biografía
- `profile.location` - Ubicación (norte, sur, este, oeste, centro)
- `profile.specialties` - Array de especialidades

### Verificar Usuario (Solo Admin)

```bash
POST /api/users/abc123.../verify
```

### Búsqueda Avanzada

```bash
POST /api/users/search
Content-Type: application/json

{
  "query": "inmobiliaria",
  "filters": {
    "location": "norte",
    "verified": true,
    "specialties": ["residential"]
  },
  "page": 1,
  "limit": 10,
  "sortBy": "createdAt",
  "sortOrder": "desc"
}
```

### Listar Usuarios con Filtros

```php
// Obtener usuarios de una ubicación específica
$usuarios = $mulbin->getUsers([
    'location' => 'norte',
    'verified' => 'true',
    'limit' => 10,
    'page' => 1
]);

if ($usuarios['status'] === 200) {
    foreach ($usuarios['data']['users'] as $user) {
        echo "Usuario: " . $user['profile']['firstName'] . " " . $user['profile']['lastName'];
        echo "Compañía: " . $user['profile']['company'];
        echo "Especialidades: " . implode(', ', $user['profile']['specialties']);
        echo "Teléfono: " . $user['profile']['phone'];
        echo "Avatar: " . $user['profile']['avatar'];
        echo "---\n";
    }
}
```

### Actualizar Avatar desde Sistema Padre

```php
$mulbin = new MulbinAPI();

// Método específico para actualizar avatar
public function updateUserAvatar($userId, $newAvatarUrl) {
    $data = [
        'profile' => [
            'avatar' => $newAvatarUrl
        ]
    ];

    return $this->makeRequest('update-user/' . $userId, 'PUT', $data);
}

// FLUJO COMPLETO: Cuando usuario cambia avatar en sistema padre
$usuarioId = $_POST['usuario_id'];
$nuevaImagenAvatar = $_FILES['nuevo_avatar'];

// 1. Procesar imagen en sistema padre
$nuevaUrlAvatar = procesarYSubirAvatar($nuevaImagenAvatar);

// 2. Actualizar en base de datos local
updateUsuarioEnSistemaPadre($usuarioId, ['avatar' => $nuevaUrlAvatar]);

// 3. Sincronizar con Mulbin
$mulbinUserId = obtenerMulbinIdPorUsuario($usuarioId);
$syncResponse = $mulbin->updateUserAvatar($mulbinUserId, $nuevaUrlAvatar);

if ($syncResponse['status'] === 200) {
    echo "✅ Avatar sincronizado en ambos sistemas";
    echo "Nueva URL: " . $syncResponse['data']['user']['profile']['avatar'];

    // El sistema automáticamente sincroniza:
    // ✓ Todos los posts del usuario
    // ✓ Todos los comentarios del usuario
    // ✓ Cache híbrido actualizado
} else {
    error_log("Error sincronizando avatar: " . json_encode($syncResponse));
    echo "⚠️ Avatar actualizado localmente, sincronización pendiente";
}

// También puedes actualizar múltiples campos a la vez
$profileUpdates = [
    'avatar' => 'https://nueva-url.jpg',
    'firstName' => 'Juan Carlos',
    'company' => 'Nueva Empresa S.A.',
    'phone' => '+523312345679'
];

$response = $mulbin->makeRequest('update-user/' . $mulbinUserId, 'PUT', [
    'profile' => $profileUpdates
]);
```

---

## 🏠 POSTS INMOBILIARIOS

### Crear Post

```bash
POST /api/posts
Content-Type: application/json
Authorization: ApiKey dev-api-key-mulbin-2024

{
  "type": "venta",
  "title": "Casa en Venta - Zona Norte",
  "description": "Hermosa casa de 3 recámaras en excelente ubicación",
  "price": 2500000,
  "location": "norte",
  "bedrooms": 3,
  "bathrooms": 2,
  "area": 150,
  "images": ["url1.jpg", "url2.jpg"],
  "authorId": "user123..."
}
```

### Listar Posts

```bash
GET /api/posts?page=1&limit=10&type=venta&location=norte&maxPrice=3000000&search=casa
```

### Obtener Post Específico

```bash
GET /api/posts/post123...
```

### Actualizar Post

```bash
PUT /api/posts/post123...
Content-Type: application/json

{
  "title": "Casa Renovada en Venta",
  "price": 2700000
}
```

### Marcar Interés en Post

```bash
POST /api/posts/post123.../interest
Authorization: Bearer <user-token>
```

### Estadísticas de Posts

```bash
GET /api/posts/stats
```

---

## 💬 COMENTARIOS

### Crear Comentario

```bash
POST /api/comments
Content-Type: application/json
Authorization: ApiKey dev-api-key-mulbin-2024

{
  "postId": "post123...",
  "text": "¿Está disponible para visita?",
  "authorId": "user123...",
  "parentCommentId": null
}
```

### Obtener Comentarios de un Post

```bash
GET /api/comments/by-post/post123...?page=1&limit=20&parentOnly=true
```

### Dar Like a Comentario

```bash
POST /api/comments/comment123.../like
Authorization: Bearer <user-token>
```

### Actualizar Comentario

```bash
PUT /api/comments/comment123...
Content-Type: application/json

{
  "text": "¿Está disponible para visita este fin de semana?"
}
```

---

## 🔔 NOTIFICACIONES

### Obtener Notificaciones del Usuario

```bash
GET /api/notifications?page=1&limit=20&unreadOnly=true
Authorization: Bearer <user-token>
```

### Crear Notificación

```bash
POST /api/notifications
Content-Type: application/json
Authorization: ApiKey dev-api-key-mulbin-2024

{
  "userId": "user123...",
  "title": "Nueva propiedad disponible",
  "message": "Se ha publicado una nueva propiedad que coincide con tus criterios",
  "type": "info",
  "icon": "home",
  "actionUrl": "/posts/post123...",
  "data": {
    "postId": "post123...",
    "category": "match"
  }
}
```

### Marcar Notificación como Leída

```bash
PUT /api/notifications/notif123.../read
```

### Marcar Todas como Leídas

```bash
PUT /api/notifications/mark-all-read
```

### Contador de No Leídas

```bash
GET /api/notifications/unread-count
```

### Crear Notificaciones en Bulk (Solo Admin)

```bash
POST /api/notifications/bulk
Content-Type: application/json
Authorization: ApiKey dev-api-key-mulbin-2024

{
  "notifications": [
    {
      "userId": "user1...",
      "title": "Mantenimiento programado",
      "message": "El sistema estará en mantenimiento mañana de 2-4 AM",
      "type": "warning",
      "icon": "warning"
    },
    {
      "userId": "user2...",
      "title": "Nueva función disponible",
      "message": "Ahora puedes filtrar propiedades por área",
      "type": "success",
      "icon": "pricetag"
    }
  ]
}
```

---

## 🔧 EJEMPLOS DE USO EN PHP

### Configuración Básica

```php
<?php
class MulbinAPI {
    private $baseUrl = 'http://localhost:3000/api/';
    private $apiKey = 'c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb';

    private function makeRequest($endpoint, $method = 'GET', $data = null) {
        $url = $this->baseUrl . $endpoint;

        $headers = [
            'Authorization: ApiKey ' . $this->apiKey,
            'Content-Type: application/json'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);

        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return [
            'status' => $httpCode,
            'data' => json_decode($response, true)
        ];
    }

    // Método helper para crear usuarios
    public function createUser($userData) {
        return $this->makeRequest('users', 'POST', $userData);
    }

    // Método helper para listar usuarios
    public function getUsers($filters = []) {
        $queryString = http_build_query($filters);
        $endpoint = 'users' . ($queryString ? '?' . $queryString : '');
        return $this->makeRequest($endpoint);
    }

    // Método helper para obtener usuario específico
    public function getUser($userId) {
        return $this->makeRequest('users/' . $userId);
    }

    // Método helper para crear posts
    public function createPost($postData) {
        return $this->makeRequest('posts', 'POST', $postData);
    }

    // Método helper para obtener estadísticas
    public function getPostsStats() {
        return $this->makeRequest('posts/stats');
    }

    // Método helper para verificar username (NUEVO)
    public function checkUsername($username) {
        return $this->makeRequest('users/check-username/' . $username);
    }

    // Método helper para actualizar usuario (NUEVO)
    public function updateUser($userId, $userData) {
        return $this->makeRequest('update-user/' . $userId, 'PUT', $userData);
    }

    // Método específico para actualizar avatar (NUEVO)
    public function updateUserAvatar($userId, $newAvatarUrl) {
        $data = [
            'profile' => [
                'avatar' => $newAvatarUrl
            ]
        ];
        return $this->makeRequest('update-user/' . $userId, 'PUT', $data);
    }

    // Método para actualizar perfil completo (NUEVO)
    public function updateUserProfile($userId, $profileData) {
        $data = [
            'profile' => $profileData
        ];
        return $this->makeRequest('update-user/' . $userId, 'PUT', $data);
    }
}
```

### Crear Usuario

```php
$mulbin = new MulbinAPI();

$newUser = [
    'email' => '<EMAIL>',
    'password' => 'password123',
    'username' => 'nuevo_corretor',
    'profile' => [
        'firstName' => 'María',
        'lastName' => 'González',
        'phone' => '+523312345678',
        'company' => 'Bienes Raíces MG',
        'license' => 'LIC-2024-003',
        'specialties' => ['residential', 'commercial'],
        'location' => 'sur',
        'bio' => 'Especialista en propiedades residenciales',
        'avatar' => 'https://cdn.example.com/avatars/maria-gonzalez.jpg'
    ],
    'roles' => ['user']
];

$response = $mulbin->createUser($newUser);

if ($response['status'] === 201) {
    echo "Usuario creado: " . $response['data']['userId'];
    echo "Username: " . $response['data']['user']['username'];
    echo "Compañía: " . $response['data']['user']['profile']['company'];
    echo "Avatar: " . $response['data']['user']['profile']['avatar'];
} else {
    echo "Error: " . $response['data']['error'];
}
```

### Verificar Username Antes de Crear (Sistema Padre)

```php
$mulbin = new MulbinAPI();

// PASO 1: Verificar si username está disponible
$usernameToCheck = 'nuevo_corretor_2024';
$checkResponse = $mulbin->checkUsername($usernameToCheck);

if ($checkResponse['status'] === 200) {
    $data = $checkResponse['data'];

    if ($data['exists']) {
        // Username ya existe
        echo "❌ Error: El username '{$usernameToCheck}' ya está en uso";
        echo "Usuario existente: {$data['user']['firstName']} {$data['user']['lastName']}";
        echo "Compañía: {$data['user']['company']}";

        // Sugerir alternativas
        $alternatives = [
            $usernameToCheck . '_2024',
            $usernameToCheck . '_mx',
            $usernameToCheck . '_pro'
        ];
        echo "Usernames sugeridos: " . implode(', ', $alternatives);

    } else {
        // Username disponible - proceder con creación
        echo "✅ Username '{$usernameToCheck}' está disponible";

        // PASO 2: Crear usuario con username verificado
        $newUser = [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'username' => $usernameToCheck, // Username verificado
            'profile' => [
                'firstName' => 'Carlos',
                'lastName' => 'Martínez',
                'phone' => '+523398765432',
                'company' => 'Propiedades Elite',
                'license' => 'LIC-2024-004',
                'specialties' => ['luxury', 'commercial'],
                'location' => 'centro',
                'bio' => 'Especialista en propiedades de alto nivel',
                'avatar' => 'https://cdn.example.com/avatars/carlos-martinez.jpg'
            ],
            'roles' => ['user']
        ];

        $createResponse = $mulbin->createUser($newUser);

        if ($createResponse['status'] === 201) {
            echo "✅ Usuario creado exitosamente";
            echo "ID: " . $createResponse['data']['userId'];
            echo "Username: " . $createResponse['data']['user']['username'];
            echo "Email: " . $createResponse['data']['user']['emails'][0]['address'];
        } else {
            echo "❌ Error al crear usuario: " . $createResponse['data']['error'];
        }
    }
} else {
    echo "❌ Error al verificar username: " . $checkResponse['data']['error'];
}
```

### Listar Usuarios con Filtros

```php
// Obtener usuarios de una ubicación específica
$usuarios = $mulbin->getUsers([
    'location' => 'norte',
    'verified' => 'true',
    'limit' => 10,
    'page' => 1
]);

if ($usuarios['status'] === 200) {
    foreach ($usuarios['data']['users'] as $user) {
        echo "Usuario: " . $user['profile']['firstName'] . " " . $user['profile']['lastName'];
        echo "Compañía: " . $user['profile']['company'];
        echo "Especialidades: " . implode(', ', $user['profile']['specialties']);
        echo "Teléfono: " . $user['profile']['phone'];
        echo "Avatar: " . $user['profile']['avatar'];
        echo "---\n";
    }
}
```

### Crear Post Inmobiliario

```php
$newPost = [
    'type' => 'venta',
    'title' => 'Departamento en Venta',
    'description' => 'Excelente ubicación, 2 recámaras',
    'price' => 1800000,
    'location' => 'centro',
    'bedrooms' => 2,
    'bathrooms' => 1,
    'authorId' => 'user123...'
];

$response = $mulbin->createPost($newPost);
```

### Enviar Notificación

```php
$notification = [
    'userId' => 'user123...',
    'title' => 'Nueva consulta',
    'message' => 'Tienes una nueva consulta sobre tu propiedad',
    'type' => 'info',
    'icon' => 'mail'
];

$response = $mulbin->createPost('notifications', 'POST', $notification);
```

---

## 📊 CÓDIGOS DE RESPUESTA

- **200** - OK
- **201** - Created
- **400** - Bad Request
- **401** - Unauthorized
- **403** - Forbidden
- **404** - Not Found
- **409** - Conflict (email/username duplicado)
- **500** - Internal Server Error

## 🛡️ SEGURIDAD

### Mejores Prácticas

1. **API Keys**: Usar variables de entorno, no hardcodear
2. **HTTPS**: En producción usar siempre HTTPS
3. **Rate Limiting**: Implementar límites de requests
4. **Validación**: Validar todos los inputs
5. **Logs**: Registrar accesos y errores

### Configuración de Producción

```bash
# Variables de entorno recomendadas
API_KEY=tu-api-key-super-segura-de-64-caracteres-minimo
MONGO_URL=*******************************:puerto/database?authSource=admin
ROOT_URL=https://tu-dominio.com
```

---

## 🔄 MIGRACIÓN DESDE MÉTODOS LEGACY

El sistema mantiene compatibilidad con métodos DDP existentes. Los frontends Vue.js pueden seguir usando:

```javascript
// DDP (sigue funcionando)
ddp.call("users.create", userData);
ddp.subscribe("postsInmobiliarios", filters);

// Nuevo REST (para backends PHP)
fetch("/api/users", { method: "POST", body: JSON.stringify(userData) });
```

---

## 📞 SOPORTE

Para dudas sobre la integración:

- Revisar logs del servidor Meteor
- Verificar autenticación y headers
- Consultar códigos de error HTTP
- Usar herramientas como Postman para testing

La API está diseñada para ser **RESTful**, **intuitiva** y **compatible** con cualquier lenguaje de programación que soporte HTTP.

---

## 🖼️ GESTIÓN DE AVATARES

### Recomendaciones para Avatares

Los avatares son fundamentales en una red social de profesionales inmobiliarios para generar confianza y profesionalismo.

#### Especificaciones Técnicas

- **Formato:** JPG, PNG, WebP
- **Tamaño:** Mínimo 200x200px, máximo 1024x1024px
- **Peso:** Máximo 2MB
- **Aspecto:** Cuadrado (1:1) recomendado
- **Calidad:** Alta resolución para profesionalismo

#### URLs de Avatar

```bash
# Ejemplos de URLs válidas
"avatar": "https://cdn.mulb.in/avatars/usuario123.jpg"
"avatar": "https://storage.example.com/profiles/maria-gonzalez.png"
"avatar": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..." # Base64 (no recomendado para producción)
```

#### Avatares por Defecto

Si no se proporciona avatar, el sistema puede usar:

```php
$defaultAvatars = [
    'male' => 'https://cdn.mulb.in/default/male-avatar.png',
    'female' => 'https://cdn.mulb.in/default/female-avatar.png',
    'company' => 'https://cdn.mulb.in/default/company-logo.png'
];
```

#### Ejemplo de Subida de Avatar

```php
// Ejemplo de función para subir avatar antes de crear usuario
function uploadAvatar($imageFile, $userId) {
    $allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];

    if (!in_array($imageFile['type'], $allowedTypes)) {
        throw new Exception('Tipo de archivo no válido');
    }

    if ($imageFile['size'] > 2 * 1024 * 1024) { // 2MB
        throw new Exception('Archivo muy grande');
    }

    // Redimensionar imagen a 512x512
    $image = imagecreatefromstring(file_get_contents($imageFile['tmp_name']));
    $resized = imagescale($image, 512, 512);

    // Guardar en CDN/Storage
    $filename = 'avatar_' . $userId . '_' . time() . '.jpg';
    $avatarUrl = uploadToCDN($resized, $filename);

    return $avatarUrl;
}

// Flujo completo
$avatarUrl = uploadAvatar($_FILES['avatar'], $newUserId);

$userData = [
    'email' => '<EMAIL>',
    'password' => 'password123',
    'profile' => [
        'firstName' => 'Juan',
        'lastName' => 'Pérez',
        'avatar' => $avatarUrl
    ]
];

$mulbin->createUser($userData);
```

---

## 🏠 CASOS DE USO PARA RED SOCIAL INMOBILIARIA

### Ejemplo Completo: Registro de Corretor con Avatar

```php
<?php
// 1. Procesar imagen de avatar subida por el usuario
$avatarUrl = '';
if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
    $avatarUrl = processAvatarUpload($_FILES['avatar']);
}

// 2. Crear perfil de corretor
$newCorretor = [
    'email' => $_POST['email'],
    'password' => $_POST['password'],
    'username' => generateUsername($_POST['firstName'], $_POST['lastName']),
    'profile' => [
        'firstName' => $_POST['firstName'],
        'lastName' => $_POST['lastName'],
        'phone' => $_POST['phone'],
        'company' => $_POST['company'],
        'license' => $_POST['license'],
        'specialties' => explode(',', $_POST['specialties']), // "residential,commercial,luxury"
        'location' => $_POST['location'], // norte, sur, este, oeste, centro
        'bio' => $_POST['bio'],
        'avatar' => $avatarUrl
    ],
    'roles' => ['user']
];

// 3. Registrar en Mulbin
$mulbin = new MulbinAPI();
$response = $mulbin->createUser($newCorretor);

if ($response['status'] === 201) {
    $user = $response['data']['user'];

    // 4. Mostrar perfil creado con avatar
    echo "<div class='user-profile'>";
    echo "<img src='{$user['profile']['avatar']}' class='avatar' />";
    echo "<h3>{$user['profile']['firstName']} {$user['profile']['lastName']}</h3>";
    echo "<p>{$user['profile']['company']}</p>";
    echo "<span class='location'>{$user['profile']['location']}</span>";
    echo "</div>";
}

function processAvatarUpload($file) {
    // Validar tipo de archivo
    $allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('Solo se permiten imágenes JPG, PNG o WebP');
    }

    // Validar tamaño (máximo 2MB)
    if ($file['size'] > 2 * 1024 * 1024) {
        throw new Exception('El archivo es muy grande. Máximo 2MB');
    }

    // Generar nombre único
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'avatar_' . uniqid() . '.' . $extension;

    // Mover a directorio de uploads
    $uploadPath = '/uploads/avatars/' . $filename;
    move_uploaded_file($file['tmp_name'], $uploadPath);

    return 'https://cdn.mulb.in/avatars/' . $filename;
}
?>
```

### Ejemplo: Directorio de Corretores

```php
// Mostrar directorio de corretores con avatares
$corretores = $mulbin->getUsers([
    'location' => 'norte',
    'verified' => 'true',
    'limit' => 20
]);

echo "<div class='corretores-grid'>";
foreach ($corretores['data']['users'] as $corretor) {
    $avatar = $corretor['profile']['avatar'] ?: '/default-avatar.png';
    $rating = number_format($corretor['profile']['rating'], 1);
    $specialties = implode(' • ', $corretor['profile']['specialties']);

    echo "<div class='corretor-card'>";
    echo "<img src='{$avatar}' class='corretor-avatar' alt='Avatar' />";
    echo "<h4>{$corretor['profile']['firstName']} {$corretor['profile']['lastName']}</h4>";
    echo "<p class='company'>{$corretor['profile']['company']}</p>";
    echo "<p class='bio'>{$corretor['profile']['bio']}</p>";
    echo "<p class='specialties'>{$specialties}</p>";
    echo "<div class='rating'>⭐ {$rating} ({$corretor['profile']['totalReviews']} reseñas)</div>";
    echo "<p class='phone'>{$corretor['profile']['phone']}</p>";
    echo "</div>";
}
echo "</div>";
```

### Ejemplo: Búsqueda de Especialistas

```php
// Buscar corretores especialistas en lujo en zona oeste
$searchResult = $mulbin->makeRequest('users/search', 'POST', [
    'query' => 'luxury',
    'filters' => [
        'location' => 'oeste',
        'specialties' => ['luxury'],
        'verified' => true
    ],
    'sortBy' => 'rating',
    'sortOrder' => 'desc',
    'limit' => 5
]);

if ($searchResult['status'] === 200) {
    $especialistas = $searchResult['data']['users'];

    echo "<h3>Especialistas en Propiedades de Lujo - Zona Oeste</h3>";
    echo "<div class='especialistas-list'>";

    foreach ($especialistas as $especialista) {
        echo "<div class='especialista-item'>";
        echo "<img src='{$especialista['profile']['avatar']}' class='avatar-small' />";
        echo "<div class='info'>";
        echo "<strong>{$especialista['profile']['firstName']} {$especialista['profile']['lastName']}</strong>";
        echo "<span class='company'>{$especialista['profile']['company']}</span>";
        echo "<span class='rating'>⭐ {$especialista['profile']['rating']}</span>";
        echo "</div>";
        echo "</div>";
    }

    echo "</div>";
}
```

---

## 🏗️ ARQUITECTURA HÍBRIDA: POSTS INMOBILIARIOS

### Problema Resuelto: Consistencia de Datos

En una red social inmobiliaria, los posts deben mostrar información actualizada del autor (nombre, avatar, empresa) pero también ser muy rápidos de consultar. La arquitectura híbrida resuelve este dilema.

#### ❌ Problema Anterior

```json
{
  "_id": "post123",
  "title": "Casa en venta",
  "author": {
    "id": "user456",
    "name": "Juan Pérez", // ¿Qué pasa si cambia su nombre?
    "avatar": "old-avatar.jpg" // ¿Qué pasa si actualiza avatar?
  }
}
```

**Problemas:**

- Si Juan actualiza su avatar, sus posts antiguos siguen mostrando el avatar viejo
- Datos inconsistentes en toda la plataforma
- Dificultad para mantener sincronización

#### ✅ Solución Híbrida Implementada

```json
{
  "_id": "post123",
  "title": "Casa en venta",
  "authorId": "user456", // ← FUENTE DE VERDAD
  "authorCache": {
    // ← CACHE AUTOMÁTICO
    "firstName": "Juan",
    "lastName": "Pérez Rodríguez",
    "avatar": "new-avatar.jpg", // ← SIEMPRE ACTUALIZADO
    "company": "Inmobiliaria ABC",
    "verified": true,
    "lastCacheUpdate": "2024-01-01T10:30:00Z"
  }
}
```

**Beneficios:**

- ✅ **Consistencia**: Cache se actualiza automáticamente
- ✅ **Performance**: Una sola query obtiene toda la info
- ✅ **Escalabilidad**: Índices optimizados
- ✅ **Confiabilidad**: Fuente de verdad preservada

### Sincronización Automática

El sistema sincroniza automáticamente cuando:

```php
// Cuando se actualiza un usuario vía REST API
PUT /api/users/user456
{
  "profile": {
    "avatar": "https://new-avatar.jpg",
    "company": "Nueva Empresa S.A."
  }
}
```

**Resultado automático:**

- ✅ Todos los posts del usuario se actualizan
- ✅ Todos los comentarios del usuario se actualizan
- ✅ Cache timestamp actualizado
- ✅ Log de sincronización generado

### Estructura Completa de Post

```json
{
  "_id": "postId123",
  "type": "venta",
  "title": "Departamento de lujo en zona exclusiva",
  "description": "Hermoso departamento con vista panorámica...",
  "price": 2500000,
  "location": "norte",

  // ✅ AUTOR HÍBRIDO
  "authorId": "user456",
  "authorCache": {
    "firstName": "Ana",
    "lastName": "Martínez",
    "avatar": "https://cdn.mulb.in/avatars/ana-martinez.jpg",
    "company": "Luxury Properties MX",
    "verified": true,
    "lastCacheUpdate": "2024-01-01T10:30:00Z"
  },

  // Detalles de la propiedad
  "propertyType": "departamento",
  "bedrooms": 3,
  "bathrooms": 2,
  "area": 120,
  "features": ["alberca", "gym", "estacionamiento", "seguridad"],

  // Preferencias de contacto
  "contactPreference": "whatsapp",
  "urgency": "media",

  // Métricas de engagement
  "views": 245,
  "contacts": 12,
  "favorites": 8,
  "interestedCount": 15,
  "commentsCount": 7,

  // Metadatos del sistema
  "createdAt": "2024-01-01T08:00:00Z",
  "updatedAt": "2024-01-01T10:30:00Z",
  "active": true
}
```

### API REST con Nueva Estructura

#### Crear Post con Autor Automático

```bash
POST /api/posts
Content-Type: application/json
Authorization: ApiKey c9214e26b8bb1af40d3d04e790ee194079e78b0ee11893dab060179ddc9075cb

{
  "type": "venta",
  "title": "Casa moderna en fraccionamiento privado",
  "description": "Hermosa casa con acabados de lujo, jardín amplio y alberca",
  "price": 3200000,
  "location": "oeste",
  "propertyType": "casa",
  "bedrooms": 4,
  "bathrooms": 3,
  "area": 280,
  "features": ["alberca", "jardín", "doble_cochera", "cuarto_servicio"],
  "contactPreference": "phone",
  "urgency": "baja",
  "authorId": "user456"
}
```

**El sistema automáticamente:**

- ✅ Busca los datos actuales del usuario
- ✅ Llena el `authorCache` con info fresca
- ✅ Inicializa métricas en 0
- ✅ Establece timestamps

#### Respuesta con Cache Completo

```json
{
  "message": "Post creado exitosamente",
  "post": {
    "_id": "newPostId789",
    "title": "Casa moderna en fraccionamiento privado",
    "authorId": "user456",
    "authorCache": {
      "firstName": "Ana",
      "lastName": "Martínez",
      "avatar": "https://cdn.mulb.in/avatars/ana-martinez.jpg",
      "company": "Luxury Properties MX",
      "verified": true,
      "lastCacheUpdate": "2024-01-01T12:00:00Z"
    },
    "views": 0,
    "contacts": 0,
    "favorites": 0
  }
}
```

### Métricas de Engagement

```bash
# Incrementar vistas
POST /api/posts/postId123/view

# Incrementar contactos (requiere autenticación)
POST /api/posts/postId123/contact
Authorization: Bearer <user-token>

# Obtener estadísticas
GET /api/posts/postId123/stats
```

### Ventajas para Red Social Inmobiliaria

**1. Confianza Profesional**

- Avatares y nombres siempre actualizados
- Badge de verificación sincronizado
- Empresa actual mostrada correctamente

**2. Performance Optimizada**

- Listados rápidos sin joins
- Índices especializados para inmobiliaria
- Cache inteligente con timestamps

**3. Métricas de Negocio**

- Tracking de engagement por corretor
- Análisis de preferencias de contacto
- Seguimiento de urgencia y conversión

**4. Escalabilidad**

- Sistema preparado para millones de posts
- Sincronización asíncrona no bloquea UI
- Índices compuestos para búsquedas complejas

Esta arquitectura garantiza que la red social inmobiliaria mantenga **consistencia profesional** mientras escala eficientemente.

---
